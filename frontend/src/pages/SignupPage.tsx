import React, { useState } from 'react'
import { But<PERSON> } from "@/components/UI/Button"
import { Input } from "@/components/UI/Input"
import { User, Lock, Mail, Facebook, Twitter, Linkedin } from 'lucide-react'
import { Link } from 'react-router-dom'

export default function SignupPage() {
  const [fullName, setFullName] = useState('')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')

  return (
    <div className="min-h-screen flex">
      {/* Left Side - Background Image with Overlay */}
      <div className="hidden lg:flex lg:w-1/2 relative">
        <div 
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: `url('/placeholder.svg?height=1080&width=960')`
          }}
        />
        <div className="absolute inset-0 bg-gradient-to-br from-teal-500/90 to-emerald-600/90" />
        
        {/* Curved Divider */}
        <div className="absolute -right-12 top-0 bottom-0 w-24 bg-white">
          <svg
            className="absolute inset-0 w-full h-full text-white"
            fill="currentColor"
            viewBox="0 0 100 100"
            preserveAspectRatio="none"
          >
            <polygon points="0,0 30,0 0,100" />
          </svg>
        </div>
      </div>

      {/* Right Side - Signup Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8 bg-gray-50">
        <div className="w-full max-w-md space-y-8">
          {/* Header */}
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">Join Us</h1>
            <p className="text-gray-600">Create your account to get started</p>
          </div>

          {/* Signup Form */}
          <form className="space-y-6">
            {/* Full Name Input */}
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <User className="h-5 w-5 text-gray-400" />
              </div>
              <Input
                type="text"
                placeholder="Full Name"
                value={fullName}
                onChange={(e) => setFullName(e.target.value)}
                className="pl-10 h-12 bg-white border-gray-300 focus:border-teal-500 focus:ring-teal-500"
              />
            </div>

            {/* Email Input */}
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Mail className="h-5 w-5 text-gray-400" />
              </div>
              <Input
                type="email"
                placeholder="Email Address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="pl-10 h-12 bg-white border-gray-300 focus:border-teal-500 focus:ring-teal-500"
              />
            </div>

            {/* Password Input */}
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Lock className="h-5 w-5 text-gray-400" />
              </div>
              <Input
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="pl-10 h-12 bg-white border-gray-300 focus:border-teal-500 focus:ring-teal-500"
              />
            </div>

            {/* Confirm Password Input */}
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Lock className="h-5 w-5 text-gray-400" />
              </div>
              <Input
                type="password"
                placeholder="Confirm Password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="pl-10 h-12 bg-white border-gray-300 focus:border-teal-500 focus:ring-teal-500"
              />
            </div>

            {/* Terms and Conditions */}
            <div className="flex items-center">
              <input
                id="terms"
                type="checkbox"
                className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
              />
              <label htmlFor="terms" className="ml-2 block text-sm text-gray-600">
                I agree to the{' '}
                <Link to="#" className="text-teal-600 hover:text-teal-700">
                  Terms and Conditions
                </Link>
              </label>
            </div>

            {/* Signup Button */}
            <Button 
              type="submit"
              className="w-full h-12 bg-teal-500 hover:bg-teal-600 text-white font-semibold rounded-full transition-colors duration-200"
            >
              Create Account
            </Button>
          </form>

          {/* Login Link */}
          <div className="text-center">
            <p className="text-gray-600">
              Already have an account?{' '}
              <Link to="/login" className="text-teal-600 hover:text-teal-700 font-medium transition-colors">
                Log in
              </Link>
            </p>
          </div>

          {/* Social Media Icons */}
          <div className="flex justify-center space-x-4 pt-4">
            <button className="p-2 text-gray-400 hover:text-blue-600 transition-colors">
              <Facebook className="h-5 w-5" />
            </button>
            <button className="p-2 text-gray-400 hover:text-blue-400 transition-colors">
              <Twitter className="h-5 w-5" />
            </button>
            <button className="p-2 text-gray-400 hover:text-blue-700 transition-colors">
              <Linkedin className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
