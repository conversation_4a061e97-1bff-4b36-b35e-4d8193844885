import React, { useState } from 'react'
import { But<PERSON> } from "@/components/UI/Button"
import { Input } from "@/components/UI/Input"
import { User, Lock, Facebook, Twitter, Linkedin } from 'lucide-react'
import { Link } from 'react-router-dom'

export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')

  return (
    <div className="min-h-screen flex relative overflow-hidden">
      {/* Left Side - Background with curved design */}
      <div className="hidden lg:block lg:w-3/5 relative">
        {/* Background Image */}
        <div
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: `url('/images/hero-background.jpg')`
          }}
        />
        {/* Teal Overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-teal-600/95 to-emerald-700/95" />

        {/* Geometric Pattern Overlay */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-20 w-32 h-32 border border-white/30 rotate-45"></div>
          <div className="absolute top-40 right-32 w-24 h-24 border border-white/20 rotate-12"></div>
          <div className="absolute bottom-32 left-32 w-40 h-40 border border-white/25 -rotate-12"></div>
        </div>
      </div>

      {/* Right Side - Curved Login Form */}
      <div className="w-full lg:w-2/5 relative bg-white">
        {/* Curved Background */}
        <div className="absolute inset-0 lg:inset-y-0 lg:left-0">
          <svg
            className="absolute inset-0 w-full h-full text-white"
            fill="currentColor"
            viewBox="0 0 100 100"
            preserveAspectRatio="none"
          >
            <path d="M0,0 C30,0 50,20 50,50 C50,80 30,100 0,100 Z" className="hidden lg:block" />
            <rect width="100" height="100" className="lg:hidden" />
          </svg>
        </div>

        {/* Form Container */}
        <div className="relative z-10 flex items-center justify-center min-h-screen p-8 lg:p-12">
          <div className="w-full max-w-sm space-y-8">
            {/* Header */}
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Welcome</h1>
              <p className="text-gray-600 text-sm">Log in to your account to continue</p>
            </div>

            {/* Login Form */}
            <form className="space-y-6">
              {/* Email Input */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <User className="h-5 w-5 text-gray-400" />
                </div>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="pl-12 h-14 bg-gray-50 border-0 rounded-2xl focus:ring-2 focus:ring-teal-500 focus:bg-white transition-all duration-200 text-gray-900 placeholder:text-gray-500"
                />
              </div>

              {/* Password Input */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <Input
                  type="password"
                  placeholder="••••••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="pl-12 h-14 bg-gray-50 border-0 rounded-2xl focus:ring-2 focus:ring-teal-500 focus:bg-white transition-all duration-200 text-gray-900 placeholder:text-gray-500"
                />
              </div>

              {/* Forgot Password */}
              <div className="text-right">
                <Link to="#" className="text-sm text-gray-500 hover:text-teal-600 transition-colors">
                  Forgot your password?
                </Link>
              </div>

              {/* Login Button */}
              <Button
                type="submit"
                className="w-full h-14 bg-gradient-to-r from-teal-500 to-emerald-600 hover:from-teal-600 hover:to-emerald-700 text-white font-semibold rounded-2xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                Log In
              </Button>
            </form>

            {/* Sign Up Link */}
            <div className="text-center">
              <p className="text-gray-600 text-sm">
                {"Don't have an account? "}
                <Link to="/signup" className="text-teal-600 hover:text-teal-700 font-medium transition-colors">
                  Sign up
                </Link>
              </p>
            </div>

            {/* Social Media Icons */}
            <div className="flex justify-center space-x-6 pt-6">
              <button className="p-3 text-gray-400 hover:text-blue-600 transition-colors rounded-full hover:bg-gray-50">
                <Facebook className="h-5 w-5" />
              </button>
              <button className="p-3 text-gray-400 hover:text-blue-400 transition-colors rounded-full hover:bg-gray-50">
                <Twitter className="h-5 w-5" />
              </button>
              <button className="p-3 text-gray-400 hover:text-blue-700 transition-colors rounded-full hover:bg-gray-50">
                <Linkedin className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
